"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Search,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Package,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Pause,
  ExternalLink,
  RotateCcw,
  Download,
  Copy,
  ShoppingCart,
  Video,
  Image as ImageIcon,
  FileText,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { productService } from "@/lib/api/products"
import PrototypeBuilder from "@/components/prototype-builder"

interface Prototype {
  id: string
  product_id: string
  analyses_id?: string
  external_job_id?: string
  custom_prompt: string
  all_data_base_best_prompt: string
  status: 'draft' | 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  file_url?: string
  file_size?: number
  file_format?: string
  metadata?: any
  error_message?: string
  media_type?: string
  duration?: number
  content_type?: string
  video_type?: string
  style?: string
  tone?: string
  voice_tone?: string
  ai_generation_type?: string
  content_description?: string
  custom_ai_prompt?: string
  target_audience?: string
  content_options?: any
  platform_distribution?: any
  lighting_style?: string
  camera_angle?: string
  camera_movement?: string
  created_at: string
  updated_at: string
  user_id: string
  Product?: {
    id: string
    title?: string
    name?: string
    description?: string
  }
  product?: {
    id: string
    name: string
    slug?: string
  }
  ProductAnalysis?: {
    id: string
    product_name: string
  }
}

interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

export default function PrototypesPage() {
  const { toast } = useToast()
  const [prototypes, setPrototypes] = useState<Prototype[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPreviousPage: false,
  })

  // Filters
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [selectedPrototype, setSelectedPrototype] = useState<Prototype | null>(null)
  const [editingPrototype, setEditingPrototype] = useState<Prototype | null>(null)
  const [showNewPrototype, setShowNewPrototype] = useState(false)

  const fetchPrototypes = async (page: number = 1) => {
    try {
      setLoading(true)

      // Check authentication (optional debug)
      // const token = document.cookie.split('; ').find(row => row.startsWith('auth_token='))?.split('=')[1]
      // console.log('Auth token available:', !!token)

      const filters: any = {}

      if (searchTerm.trim()) {
        filters.search = searchTerm.trim()
      }

      if (statusFilter !== "all") {
        filters.status = statusFilter
      }

      const response = await productService.getAllPrototypes(page, 10, filters)

      // console.log('API Response:', response) // Debug log
      // console.log('Prototypes data:', response.data) // Debug log

      if (response.success) {
        const prototypesData = response.data.prototypes || response.data.data || response.data || []
        // console.log('Processed prototypes:', prototypesData) // Debug log

        setPrototypes(prototypesData)

        // Handle pagination based on API response structure
        const paginationData = response.data.pagination || {
          currentPage: response.data.page || 1,
          totalPages: response.data.totalPages || 1,
          totalItems: response.data.total || prototypesData.length,
          itemsPerPage: response.data.limit || 10,
          hasNextPage: (response.data.page || 1) < (response.data.totalPages || 1),
          hasPreviousPage: (response.data.page || 1) > 1,
        }

        setPagination(paginationData)
      } else {
        console.error('API Error:', response)
        throw new Error(response.message || 'Failed to fetch prototypes')
      }
    } catch (error: any) {
      console.error('Error fetching prototypes:', error)

      // Show more detailed error message
      let errorMessage = 'Failed to fetch prototypes'
      if (error.message?.includes('Authentication')) {
        errorMessage = 'Authentication required. Please login again.'
      } else if (error.message?.includes('Network')) {
        errorMessage = 'Network error. Please check your connection.'
      } else if (error.message) {
        errorMessage = error.message
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPrototypes(1)
  }, [searchTerm, statusFilter])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
  }

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value)
  }

  const handlePageChange = (page: number) => {
    fetchPrototypes(page)
  }

  const handleDeletePrototype = async (prototypeId: string) => {
    try {
      const response = await productService.deletePrototype(prototypeId)
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Prototype deleted successfully',
        })
        fetchPrototypes(pagination.currentPage)
      } else {
        throw new Error(response.message || 'Failed to delete prototype')
      }
    } catch (error: any) {
      console.error('Error deleting prototype:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete prototype',
        variant: 'destructive'
      })
    }
  }

  const handleEditPrototype = (prototype: Prototype) => {
    setEditingPrototype(prototype)
    setShowNewPrototype(true)
  }

  const handleBackToList = () => {
    setShowNewPrototype(false)
    setEditingPrototype(null)
  }

  const handleEditSuccess = () => {
    handleBackToList()
    fetchPrototypes(pagination.currentPage)
  }

  const handleSyncPrototype = async (prototype: Prototype) => {
    if (!prototype.external_job_id) {
      toast({
        title: 'Error',
        description: 'No job ID found for this prototype',
        variant: 'destructive'
      })
      return
    }

    try {
      const response = await productService.syncPrototypeStatus(prototype.external_job_id)
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Prototype status synced successfully',
        })
        fetchPrototypes(pagination.currentPage)
      } else {
        throw new Error(response.message || 'Failed to sync prototype status')
      }
    } catch (error: any) {
      console.error('Error syncing prototype:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to sync prototype status',
        variant: 'destructive'
      })
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="h-4 w-4" />
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'processing':
        return <RefreshCw className="h-4 w-4 animate-spin" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'failed':
        return <XCircle className="h-4 w-4" />
      case 'cancelled':
        return <Pause className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'processing':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatFileSize = (bytes?: number | null) => {
    if (!bytes || bytes === null || bytes === 0) return 'N/A'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDuration = (seconds?: number | null) => {
    if (!seconds || seconds === null || seconds === 0) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatValue = (value?: string | null) => {
    if (!value || value === null || value === '') return 'N/A'
    // Replace underscores with spaces and capitalize first letter
    return value.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const handleDownloadFile = (fileUrl: string, filename?: string) => {
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = filename || 'prototype-file'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleCopyMediaLink = async (fileUrl: string) => {
    try {
      await navigator.clipboard.writeText(fileUrl)
      toast({
        title: 'Success',
        description: 'Media link copied to clipboard',
      })
    } catch (error) {
      console.error('Failed to copy link:', error)
      toast({
        title: 'Error',
        description: 'Failed to copy media link',
        variant: 'destructive'
      })
    }
  }

  const handleMarkAsProduction = async (prototype: Prototype) => {
    try {
      const result = await productService.markPrototypeAsProduction(prototype.id)

      // Update the prototype in local state
      setPrototypes(prev => prev.map(p =>
        p.id === prototype.id
          ? {
              ...p,
              metadata: {
                ...p.metadata,
                marked_as_production: true,
                production_media_id: result.data?.media?.id,
                marked_production_at: new Date().toISOString()
              }
            }
          : p
      ))

      // Update selected prototype if it's the same one
      if (selectedPrototype?.id === prototype.id) {
        setSelectedPrototype(prev => prev ? {
          ...prev,
          metadata: {
            ...prev.metadata,
            marked_as_production: true,
            production_media_id: result.data?.media?.id,
            marked_production_at: new Date().toISOString()
          }
        } : null)
      }

      toast({
        title: 'Success',
        description: result.message || 'Prototype has been marked as production successfully.',
      })
    } catch (error: any) {
      console.error('Error marking prototype as production:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to mark prototype as production',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center gap-2">
            <Package className="h-6 w-6 sm:h-8 sm:w-8" />
            Prototype Management
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Manage all your product prototypes in one place
          </p>
        </div>

      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search prototypes..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Prototypes Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">
            Prototypes ({pagination.totalItems})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0 sm:p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 sm:h-8 sm:w-8 animate-spin" />
              <span className="ml-2 text-sm sm:text-base">Loading prototypes...</span>
            </div>
          ) : prototypes.length === 0 ? (
            <div className="text-center py-8 px-4">
              <Package className="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">No prototypes found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || statusFilter !== "all"
                  ? "Try adjusting your filters"
                  : "Get started by creating your first prototype"}
              </p>

            </div>
          ) : (
            <div className="rounded-md border overflow-hidden">
              {/* Mobile Card View */}
              <div className="block sm:hidden">
                {prototypes.map((prototype) => (
                  <div key={prototype.id} className="border-b last:border-b-0 p-4 space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm ">
                          {prototype.Product?.title || prototype.Product?.name || prototype.product?.name || 'Loading...'}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {prototype.content_description || prototype.custom_prompt.substring(0, 80) + '...'}
                        </div>
                      </div>
                      <Badge className={`${getStatusColor(prototype.status)} flex items-center gap-1 ml-2 text-xs`}>
                        {getStatusIcon(prototype.status)}
                        {prototype.status.charAt(0).toUpperCase() + prototype.status.slice(1)}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {formatValue(prototype.media_type)}
                        </Badge>
                        <span className="text-muted-foreground">
                          {new Date(prototype.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>

                    {prototype.file_url && (
                      <div className="flex items-center space-x-2">
                        <a
                          href={prototype.file_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 hover:underline text-xs"
                        >
                          <ExternalLink className="h-3 w-3" />
                          <span>View File</span>
                        </a>
                        {prototype.file_size && (
                          <span className="text-xs text-muted-foreground">
                            {formatFileSize(prototype.file_size)}
                          </span>
                        )}
                      </div>
                    )}

                    <div className="flex items-center justify-end space-x-1">
                      {prototype.status === 'pending' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSyncPrototype(prototype)}
                          title="Sync Status"
                          className="h-8 w-8 p-0"
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                      )}
                      {prototype.status !== 'completed' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditPrototype(prototype)}
                          title="Edit Prototype"
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedPrototype(prototype)}
                        title="View Details"
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeletePrototype(prototype.id)}
                        title="Delete Prototype"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Desktop Table View */}
              <div className="hidden sm:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="hidden lg:table-cell">Media Type</TableHead>
                      <TableHead className="hidden md:table-cell">File</TableHead>
                      <TableHead className="hidden lg:table-cell">Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {prototypes.map((prototype) => (
                      <TableRow key={prototype.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium text-sm">
                              {prototype.Product?.title || prototype.Product?.name || prototype.product?.name || 'Loading...'}
                            </div>
                            <div className="text-xs text-muted-foreground line-clamp-2">
                              {prototype.content_description || prototype.custom_prompt.substring(0, 50) + '...'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={`${getStatusColor(prototype.status)} flex items-center gap-1 w-fit text-xs`}>
                            {getStatusIcon(prototype.status)}
                            <span className="hidden sm:inline">
                              {prototype.status.charAt(0).toUpperCase() + prototype.status.slice(1)}
                            </span>
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <Badge variant="outline" className="text-xs">
                            {formatValue(prototype.media_type)}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="flex flex-col space-y-1">
                            {prototype.file_url ? (
                              <div className="flex items-center space-x-2">
                                <a
                                  href={prototype.file_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 hover:underline"
                                >
                                  <ExternalLink className="h-3 w-3" />
                                  <span className="text-xs">View File</span>
                                </a>
                              </div>
                            ) : (
                              <span className="text-xs text-muted-foreground">No file</span>
                            )}

                            {/* File info - condensed for responsive */}
                            <div className="flex flex-col text-xs text-muted-foreground">
                              {prototype.file_size && (
                                <span>{formatFileSize(prototype.file_size)}</span>
                              )}
                              {prototype.duration && (
                                <span>{formatDuration(prototype.duration)}</span>
                              )}
                            </div>

                            {/* Status indicators */}
                            {!prototype.file_url && prototype.status === 'draft' && (
                              <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                Pending
                              </span>
                            )}
                            {!prototype.file_url && prototype.status === 'processing' && (
                              <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                Generating...
                              </span>
                            )}
                            {!prototype.file_url && prototype.status === 'failed' && (
                              <span className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                                Failed
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <div className="flex flex-col">
                            <span className="text-xs">
                              {new Date(prototype.created_at).toLocaleDateString()}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(prototype.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-1">
                            {/* Sync button - only show for pending status */}
                            {prototype.status === 'pending' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleSyncPrototype(prototype)}
                                title="Sync Status"
                                className="h-8 w-8 p-0"
                              >
                                <RotateCcw className="h-4 w-4" />
                              </Button>
                            )}

                            {/* Edit button - only show if status is not completed */}
                            {prototype.status !== 'completed' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditPrototype(prototype)}
                                title="Edit Prototype"
                                className="h-8 w-8 p-0"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}

                            {/* View Details button */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedPrototype(prototype)}
                              title="View Details"
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>

                            {/* Delete button */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeletePrototype(prototype.id)}
                              title="Delete Prototype"
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {!loading && prototypes.length > 0 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
            Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
            {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
            {pagination.totalItems} prototypes
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={!pagination.hasPreviousPage}
              className="text-xs sm:text-sm"
            >
              <span className="hidden sm:inline">Previous</span>
              <span className="sm:hidden">Prev</span>
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(3, pagination.totalPages) }, (_, i) => {
                const pageNumber = Math.max(1, pagination.currentPage - 1) + i
                if (pageNumber > pagination.totalPages) return null
                return (
                  <Button
                    key={pageNumber}
                    variant={pageNumber === pagination.currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNumber)}
                    className="text-xs sm:text-sm min-w-[32px] sm:min-w-[36px]"
                  >
                    {pageNumber}
                  </Button>
                )
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={!pagination.hasNextPage}
              className="text-xs sm:text-sm"
            >
              <span className="hidden sm:inline">Next</span>
              <span className="sm:hidden">Next</span>
            </Button>
          </div>
        </div>
      )}

      {/* Prototype Detail Dialog */}
      <Dialog open={!!selectedPrototype} onOpenChange={() => setSelectedPrototype(null)}>
        <DialogContent className="max-w-[95vw] sm:max-w-3xl lg:max-w-5xl rounded-lg max-h-[95vh] overflow-hidden">
          <DialogHeader className="pb-4 border-b">
            <DialogTitle className="text-lg sm:text-xl font-semibold flex items-center gap-2">
              <Package className="h-4 w-4 sm:h-5 sm:w-5" />
              Prototype Details
            </DialogTitle>
          </DialogHeader>
          {selectedPrototype && (
            <ScrollArea className="max-h-[75vh] pr-2 sm:pr-4">
              <div className="space-y-4 sm:space-y-8 py-4">
                {/* Header Section with Key Info */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-lg border">
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                    <div className="space-y-3 flex-1 min-w-0">
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 break-words">
                        {selectedPrototype.Product?.title || selectedPrototype.Product?.name || selectedPrototype.product?.name || 'Unknown Product'}
                      </h3>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                        <Badge className={`${getStatusColor(selectedPrototype.status)} flex items-center gap-1 px-2 sm:px-3 py-1 w-fit`}>
                          {getStatusIcon(selectedPrototype.status)}
                          <span className="text-xs sm:text-sm">
                            {selectedPrototype.status.charAt(0).toUpperCase() + selectedPrototype.status.slice(1)}
                          </span>
                        </Badge>
                        <Badge variant="outline" className="px-2 sm:px-3 py-1 w-fit text-xs sm:text-sm">
                          {formatValue(selectedPrototype.media_type)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
                  {/* Basic Information Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm sm:text-base flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Basic Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs sm:text-sm">
                        <div>
                          <span className="text-muted-foreground">Duration:</span>
                          <p className="font-medium break-words">{formatDuration(selectedPrototype.duration)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">File Size:</span>
                          <p className="font-medium break-words">{formatFileSize(selectedPrototype.file_size)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Format:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.file_format)}</p>
                        </div>
                        {selectedPrototype.external_job_id && (
                          <div className="sm:col-span-2">
                            <span className="text-muted-foreground">Job ID:</span>
                            <p className="font-medium font-mono text-xs break-all">{selectedPrototype.external_job_id}</p>
                          </div>
                        )}
                      </div>
                      <div className="pt-2 border-t">
                        <div className="grid grid-cols-1 gap-2 text-xs sm:text-sm">
                          <div>
                            <span className="text-muted-foreground">Created:</span>
                            <p className="font-medium break-words">{new Date(selectedPrototype.created_at).toLocaleString()}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Updated:</span>
                            <p className="font-medium break-words">{new Date(selectedPrototype.updated_at).toLocaleString()}</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Content Settings Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm sm:text-base flex items-center gap-2">
                        <Edit className="h-4 w-4" />
                        Content Settings
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs sm:text-sm">
                        <div>
                          <span className="text-muted-foreground">Content Type:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.content_type)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Video Type:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.video_type)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Style:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.style)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Tone:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.tone)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Voice Tone:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.voice_tone)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">AI Generation:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.ai_generation_type)}</p>
                        </div>
                      </div>
                      <div className="pt-2 border-t">
                        <div className="grid grid-cols-1 gap-2 text-xs sm:text-sm">
                          <div>
                            <span className="text-muted-foreground">Target Audience:</span>
                            <p className="font-medium break-words">{formatValue(selectedPrototype.target_audience)}</p>
                          </div>
                          {selectedPrototype.platform_distribution && Object.keys(selectedPrototype.platform_distribution).length > 0 && (
                            <div>
                              <span className="text-muted-foreground">Target Platforms:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {Object.keys(selectedPrototype.platform_distribution).map((platform) => (
                                  <Badge key={platform} variant="secondary" className="text-xs">
                                    {formatValue(platform)}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Camera & Lighting Settings Card */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm sm:text-base flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        Visual Settings
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs sm:text-sm">
                        <div>
                          <span className="text-muted-foreground">Lighting Style:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.lighting_style)}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Camera Angle:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.camera_angle)}</p>
                        </div>
                        <div className="sm:col-span-2">
                          <span className="text-muted-foreground">Camera Movement:</span>
                          <p className="font-medium break-words">{formatValue(selectedPrototype.camera_movement)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* File Preview Card */}
                  {selectedPrototype.file_url && (
                    <Card className="xl:col-span-2">
                      <CardHeader>
                        <CardTitle className="text-sm sm:text-base flex items-center gap-2">
                          <Package className="h-4 w-4" />
                          File Preview
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-center bg-gray-50 rounded-lg p-2 sm:p-4">
                          {selectedPrototype.media_type === 'video' || selectedPrototype.file_format?.toLowerCase().includes('mp4') || selectedPrototype.file_format?.toLowerCase().includes('mov') || selectedPrototype.file_format?.toLowerCase().includes('avi') ? (
                            <video
                              src={selectedPrototype.file_url}
                              controls
                              className="max-w-full max-h-48 sm:max-h-64 rounded-lg shadow-lg"
                              preload="metadata"
                            >
                              Your browser does not support the video tag.
                            </video>
                          ) : selectedPrototype.media_type === 'image' || selectedPrototype.file_format?.toLowerCase().includes('jpg') || selectedPrototype.file_format?.toLowerCase().includes('jpeg') || selectedPrototype.file_format?.toLowerCase().includes('png') || selectedPrototype.file_format?.toLowerCase().includes('gif') || selectedPrototype.file_format?.toLowerCase().includes('webp') ? (
                            <img
                              src={selectedPrototype.file_url}
                              alt="Prototype preview"
                              className="max-w-full max-h-48 sm:max-h-64 rounded-lg shadow-lg object-contain"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const parent = target.parentElement;
                                if (parent) {
                                  parent.innerHTML = `
                                    <div class="flex flex-col items-center justify-center p-4 sm:p-8 text-gray-500">
                                      <div class="w-6 h-6 sm:w-8 sm:h-8 mb-2">⚠️</div>
                                      <p class="text-xs sm:text-sm">Unable to load preview</p>
                                    </div>
                                  `;
                                }
                              }}
                            />
                          ) : (
                            <div className="flex flex-col items-center justify-center p-4 sm:p-8 text-gray-500">
                              <Package className="h-6 w-6 sm:h-8 sm:w-8 mb-2" />
                              <p className="text-xs sm:text-sm font-medium mb-1">Preview Not Available</p>
                              <p className="text-xs text-center break-words">
                                {selectedPrototype.file_format || 'Unknown format'}
                              </p>
                            </div>
                          )}
                        </div>
                        {/* File Info */}
                        <div className="p-2 sm:p-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                            <div className="flex items-center gap-2">
                              {selectedPrototype.media_type === 'video' || selectedPrototype.file_format?.toLowerCase().includes('mp4') || selectedPrototype.file_format?.toLowerCase().includes('mov') || selectedPrototype.file_format?.toLowerCase().includes('avi') ? (
                                <Video className="h-4 w-4 text-gray-500" />
                              ) : selectedPrototype.media_type === 'image' || selectedPrototype.file_format?.toLowerCase().includes('jpg') || selectedPrototype.file_format?.toLowerCase().includes('jpeg') || selectedPrototype.file_format?.toLowerCase().includes('png') || selectedPrototype.file_format?.toLowerCase().includes('gif') || selectedPrototype.file_format?.toLowerCase().includes('webp') ? (
                                <ImageIcon className="h-4 w-4 text-gray-500" />
                              ) : (
                                <FileText className="h-4 w-4 text-gray-500" />
                              )}
                              <span className="font-medium text-xs sm:text-sm break-words">
                                {selectedPrototype.media_type === 'video' || selectedPrototype.file_format?.toLowerCase().includes('mp4') ? 'MP4 video' :
                                 selectedPrototype.media_type === 'image' || selectedPrototype.file_format?.toLowerCase().includes('jpg') || selectedPrototype.file_format?.toLowerCase().includes('jpeg') ? 'JPG image' :
                                 selectedPrototype.media_type || 'File'}
                              </span>
                            </div>
                            <div className="flex items-center gap-1 sm:gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(selectedPrototype.file_url!, '_blank')}
                                title="View Details"
                                className="p-1 sm:p-2 h-7 w-7 sm:h-8 sm:w-8"
                              >
                                <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCopyMediaLink(selectedPrototype.file_url!)}
                                title="Copy Media Link"
                                className="p-1 sm:p-2 h-7 w-7 sm:h-8 sm:w-8"
                              >
                                <Copy className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDownloadFile(selectedPrototype.file_url!, `prototype-${selectedPrototype.id}`)}
                                title="Download File"
                                className="p-1 sm:p-2 h-7 w-7 sm:h-8 sm:w-8"
                              >
                                <Download className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Make as Production Button and Status */}
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                            {selectedPrototype.status === 'completed' && !selectedPrototype.metadata?.marked_as_production && (
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleMarkAsProduction(selectedPrototype)}
                                className="px-2 sm:px-3 py-1 h-7 sm:h-8 bg-green-600 hover:bg-green-700 text-white text-xs sm:text-sm"
                              >
                                <ShoppingCart className="h-3 w-3 mr-1" />
                                <span className="hidden sm:inline">Make as Production</span>
                                <span className="sm:hidden">Production</span>
                              </Button>
                            )}
                            {selectedPrototype.metadata?.marked_as_production && (
                              <div className="flex items-center gap-2 text-green-700">
                                <ShoppingCart className="h-3 w-3" />
                                <span className="text-xs font-medium break-words">
                                  <span className="hidden sm:inline">This file already Made as Production</span>
                                  <span className="sm:hidden">Made as Production</span>
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Prompts Section */}
                <div className="space-y-4">
                  <h4 className="text-base sm:text-lg font-semibold">Prompts & Descriptions</h4>
                  <div className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-xs sm:text-sm font-medium text-blue-700">Custom Prompt</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="p-3 sm:p-4 bg-blue-50 rounded-lg text-xs sm:text-sm leading-relaxed break-words">
                          {selectedPrototype.custom_prompt}
                        </div>
                      </CardContent>
                    </Card>

                    {selectedPrototype.all_data_base_best_prompt && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-xs sm:text-sm font-medium text-green-700">AI Generated Prompt</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="p-3 sm:p-4 bg-green-50 rounded-lg text-xs sm:text-sm leading-relaxed break-words">
                            {selectedPrototype.all_data_base_best_prompt}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {selectedPrototype.content_description && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-xs sm:text-sm font-medium text-purple-700">Content Description</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="p-3 sm:p-4 bg-purple-50 rounded-lg text-xs sm:text-sm leading-relaxed break-words">
                            {selectedPrototype.content_description}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>

                {/* Error Message */}
                {selectedPrototype.error_message && (
                  <Card className="border-red-200">
                    <CardHeader>
                      <CardTitle className="text-xs sm:text-sm font-medium text-red-700 flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        Error Message
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="p-3 sm:p-4 bg-red-50 rounded-lg text-xs sm:text-sm text-red-800 leading-relaxed break-words">
                        {selectedPrototype.error_message}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Prototype Modal */}
      <Dialog open={showNewPrototype} onOpenChange={setShowNewPrototype}>
        <DialogContent className="max-w-[95vw] sm:max-w-2xl lg:max-w-4xl rounded-lg max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">
              {editingPrototype ? 'Edit Prototype' : 'Create New Prototype'}
            </DialogTitle>
          </DialogHeader>
          <PrototypeBuilder
            productId={editingPrototype?.product_id || ''}
            analysesId={editingPrototype?.analyses_id || ''}
            editingPrototype={editingPrototype}
            productData={null}
            analysisData={null}
            onSuccess={handleEditSuccess}
            inDialog={true}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { toast } from "@/components/ui/use-toast"
import { format, addDays, addHours, isBefore, formatDistanceToNow } from "date-fns"
import {
  CalendarIcon,
  Clock,
  Trash2,
  Plus,
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  XCircle,
  RefreshCw,
  BellOff,
  CalendarPlus2Icon as CalendarIcon2,
  Save,
} from "lucide-react"
import { cn } from "@/lib/utils"

interface ScheduledCheck {
  id: string
  name: string
  frequency: "hourly" | "daily" | "weekly" | "monthly" | "custom"
  nextRun: Date
  lastRun?: Date
  lastStatus?: "operational" | "degraded" | "outage"
  enabled: boolean
  days?: number[] // 0-6, 0 is Sunday
  time?: string // HH:MM format
  notifyOnIssues: boolean
  notifyOnSuccess: boolean
}

export function ScheduledChecks({ onRunCheck }: { onRunCheck: () => void }) {
  const [activeTab, setActiveTab] = useState("upcoming")
  const [scheduledChecks, setScheduledChecks] = useState<ScheduledCheck[]>([])
  const [checkHistory, setCheckHistory] = useState<
    {
      id: string
      checkId: string
      name: string
      runAt: Date
      status: "operational" | "degraded" | "outage"
      issuesFound: number
    }[]
  >([])
  const [isAddingCheck, setIsAddingCheck] = useState(false)
  const [newCheck, setNewCheck] = useState<Omit<ScheduledCheck, "id" | "nextRun"> & { nextRun?: Date }>({
    name: "System Check",
    frequency: "daily",
    enabled: true,
    notifyOnIssues: true,
    notifyOnSuccess: false,
    days: [1, 2, 3, 4, 5], // Monday to Friday
    time: "09:00",
  })
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [isEditingCheck, setIsEditingCheck] = useState<string | null>(null)

  // Initialize with some example scheduled checks
  useEffect(() => {
    const initialChecks: ScheduledCheck[] = [
      {
        id: "daily-morning",
        name: "Daily Morning Check",
        frequency: "daily",
        nextRun: addHours(new Date().setHours(9, 0, 0, 0), 24),
        lastRun: new Date(new Date().setHours(9, 0, 0, 0)),
        lastStatus: "operational",
        enabled: true,
        time: "09:00",
        notifyOnIssues: true,
        notifyOnSuccess: false,
      },
      {
        id: "weekly-monday",
        name: "Weekly Comprehensive Check",
        frequency: "weekly",
        nextRun: addDays(new Date().setHours(7, 0, 0, 0), 7),
        lastRun: new Date(new Date().setHours(7, 0, 0, 0)),
        lastStatus: "degraded",
        enabled: true,
        days: [1], // Monday
        time: "07:00",
        notifyOnIssues: true,
        notifyOnSuccess: true,
      },
      {
        id: "hourly-business",
        name: "Hourly Business Hours Check",
        frequency: "hourly",
        nextRun: addHours(new Date(), 1),
        lastRun: new Date(),
        lastStatus: "operational",
        enabled: false,
        days: [1, 2, 3, 4, 5], // Monday to Friday
        notifyOnIssues: true,
        notifyOnSuccess: false,
      },
    ]

    setScheduledChecks(initialChecks)

    // Initialize with some example check history
    const initialHistory = [
      {
        id: "hist-1",
        checkId: "daily-morning",
        name: "Daily Morning Check",
        runAt: new Date(new Date().setHours(9, 0, 0, 0)),
        status: "operational" as const,
        issuesFound: 0,
      },
      {
        id: "hist-2",
        checkId: "weekly-monday",
        name: "Weekly Comprehensive Check",
        runAt: new Date(new Date().setHours(7, 0, 0, 0)),
        status: "degraded" as const,
        issuesFound: 2,
      },
      {
        id: "hist-3",
        checkId: "daily-morning",
        name: "Daily Morning Check",
        runAt: addDays(new Date().setHours(9, 0, 0, 0), -1),
        status: "operational" as const,
        issuesFound: 0,
      },
      {
        id: "hist-4",
        checkId: "weekly-monday",
        name: "Weekly Comprehensive Check",
        runAt: addDays(new Date().setHours(7, 0, 0, 0), -7),
        status: "outage" as const,
        issuesFound: 5,
      },
      {
        id: "hist-5",
        checkId: "daily-morning",
        name: "Daily Morning Check",
        runAt: addDays(new Date().setHours(9, 0, 0, 0), -2),
        status: "degraded" as const,
        issuesFound: 1,
      },
    ]

    setCheckHistory(initialHistory)
  }, [])

  // Simulate check execution timer
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date()

      // Check if any scheduled checks need to run
      setScheduledChecks((prev) => {
        let checksUpdated = false
        const updated = prev.map((check) => {
          if (check.enabled && isBefore(check.nextRun, now)) {
            checksUpdated = true

            // Run the check
            onRunCheck()

            // Generate random status for simulation
            const rand = Math.random()
            let status: "operational" | "degraded" | "outage"
            let issuesFound = 0

            if (rand < 0.7) {
              status = "operational"
              issuesFound = 0
            } else if (rand < 0.9) {
              status = "degraded"
              issuesFound = Math.floor(Math.random() * 3) + 1
            } else {
              status = "outage"
              issuesFound = Math.floor(Math.random() * 5) + 3
            }

            // Add to history
            setCheckHistory((prev) => [
              {
                id: `hist-${Date.now()}`,
                checkId: check.id,
                name: check.name,
                runAt: now,
                status,
                issuesFound,
              },
              ...prev,
            ])

            // Show notification
            if (
              (status !== "operational" && check.notifyOnIssues) ||
              (status === "operational" && check.notifyOnSuccess)
            ) {
              toast({
                title: `Scheduled Check: ${check.name}`,
                description: status === "operational" ? "All systems operational" : `${issuesFound} issues detected`,
                variant: status === "operational" ? "default" : status === "degraded" ? "warning" : "destructive",
              })
            }

            // Calculate next run time based on frequency
            let nextRun: Date
            switch (check.frequency) {
              case "hourly":
                nextRun = addHours(now, 1)
                break
              case "daily":
                nextRun = addDays(now, 1)
                if (check.time) {
                  const [hours, minutes] = check.time.split(":").map(Number)
                  nextRun.setHours(hours, minutes, 0, 0)
                }
                break
              case "weekly":
                if (check.days && check.days.length > 0) {
                  // Find the next day in the week that matches
                  let daysToAdd = 1
                  while (!check.days.includes((now.getDay() + daysToAdd) % 7)) {
                    daysToAdd++
                  }
                  nextRun = addDays(now, daysToAdd)
                } else {
                  nextRun = addDays(now, 7)
                }
                if (check.time) {
                  const [hours, minutes] = check.time.split(":").map(Number)
                  nextRun.setHours(hours, minutes, 0, 0)
                }
                break
              case "monthly":
                // Simple implementation - just add 30 days
                nextRun = addDays(now, 30)
                if (check.time) {
                  const [hours, minutes] = check.time.split(":").map(Number)
                  nextRun.setHours(hours, minutes, 0, 0)
                }
                break
              default:
                nextRun = addDays(now, 1)
            }

            return {
              ...check,
              lastRun: now,
              lastStatus: status,
              nextRun,
            }
          }
          return check
        })

        if (checksUpdated) {
          return updated
        }
        return prev
      })
    }, 10000) // Check every 10 seconds (for demo purposes)

    return () => clearInterval(timer)
  }, [onRunCheck])

  const handleAddCheck = () => {
    if (!newCheck.name || !newCheck.frequency) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      })
      return
    }

    // Calculate next run time
    let nextRun = new Date()

    switch (newCheck.frequency) {
      case "hourly":
        nextRun = addHours(nextRun, 1)
        break
      case "daily":
        nextRun = addDays(nextRun, 1)
        if (newCheck.time) {
          const [hours, minutes] = newCheck.time.split(":").map(Number)
          nextRun.setHours(hours, minutes, 0, 0)
        }
        break
      case "weekly":
        if (newCheck.days && newCheck.days.length > 0) {
          // Find the next day in the week that matches
          let daysToAdd = 1
          const currentDay = nextRun.getDay()
          while (!newCheck.days.includes((currentDay + daysToAdd) % 7)) {
            daysToAdd++
          }
          nextRun = addDays(nextRun, daysToAdd)
        } else {
          nextRun = addDays(nextRun, 7)
        }
        if (newCheck.time) {
          const [hours, minutes] = newCheck.time.split(":").map(Number)
          nextRun.setHours(hours, minutes, 0, 0)
        }
        break
      case "monthly":
        // Simple implementation - just add 30 days
        nextRun = addDays(nextRun, 30)
        if (newCheck.time) {
          const [hours, minutes] = newCheck.time.split(":").map(Number)
          nextRun.setHours(hours, minutes, 0, 0)
        }
        break
      case "custom":
        if (newCheck.nextRun) {
          nextRun = newCheck.nextRun
        }
        break
    }

    // If the calculated next run is in the past, adjust it
    if (isBefore(nextRun, new Date())) {
      if (newCheck.frequency === "hourly") {
        nextRun = addHours(new Date(), 1)
      } else {
        nextRun = addDays(nextRun, 1)
      }
    }

    const newScheduledCheck: ScheduledCheck = {
      id: `check-${Date.now()}`,
      name: newCheck.name,
      frequency: newCheck.frequency,
      nextRun,
      enabled: newCheck.enabled,
      days: newCheck.days,
      time: newCheck.time,
      notifyOnIssues: newCheck.notifyOnIssues,
      notifyOnSuccess: newCheck.notifyOnSuccess,
    }

    setScheduledChecks((prev) => [...prev, newScheduledCheck])
    setIsAddingCheck(false)

    // Reset form
    setNewCheck({
      name: "System Check",
      frequency: "daily",
      enabled: true,
      notifyOnIssues: true,
      notifyOnSuccess: false,
      days: [1, 2, 3, 4, 5], // Monday to Friday
      time: "09:00",
    })

    toast({
      title: "Scheduled Check Added",
      description: `${newScheduledCheck.name} has been scheduled for ${format(nextRun, "PPpp")}`,
    })
  }

  const handleUpdateCheck = (checkId: string) => {
    if (!isEditingCheck) return

    setScheduledChecks((prev) =>
      prev.map((check) => {
        if (check.id === checkId) {
          // Calculate next run time if needed
          let nextRun = check.nextRun

          if (
            check.frequency !== newCheck.frequency ||
            check.time !== newCheck.time ||
            JSON.stringify(check.days) !== JSON.stringify(newCheck.days)
          ) {
            nextRun = new Date()

            switch (newCheck.frequency) {
              case "hourly":
                nextRun = addHours(nextRun, 1)
                break
              case "daily":
                nextRun = addDays(nextRun, 1)
                if (newCheck.time) {
                  const [hours, minutes] = newCheck.time.split(":").map(Number)
                  nextRun.setHours(hours, minutes, 0, 0)
                }
                break
              case "weekly":
                if (newCheck.days && newCheck.days.length > 0) {
                  // Find the next day in the week that matches
                  let daysToAdd = 1
                  const currentDay = nextRun.getDay()
                  while (!newCheck.days.includes((currentDay + daysToAdd) % 7)) {
                    daysToAdd++
                  }
                  nextRun = addDays(nextRun, daysToAdd)
                } else {
                  nextRun = addDays(nextRun, 7)
                }
                if (newCheck.time) {
                  const [hours, minutes] = newCheck.time.split(":").map(Number)
                  nextRun.setHours(hours, minutes, 0, 0)
                }
                break
              case "monthly":
                // Simple implementation - just add 30 days
                nextRun = addDays(nextRun, 30)
                if (newCheck.time) {
                  const [hours, minutes] = newCheck.time.split(":").map(Number)
                  nextRun.setHours(hours, minutes, 0, 0)
                }
                break
              case "custom":
                if (newCheck.nextRun) {
                  nextRun = newCheck.nextRun
                }
                break
            }

            // If the calculated next run is in the past, adjust it
            if (isBefore(nextRun, new Date())) {
              if (newCheck.frequency === "hourly") {
                nextRun = addHours(new Date(), 1)
              } else {
                nextRun = addDays(nextRun, 1)
              }
            }
          }

          return {
            ...check,
            name: newCheck.name,
            frequency: newCheck.frequency,
            nextRun,
            enabled: newCheck.enabled,
            days: newCheck.days,
            time: newCheck.time,
            notifyOnIssues: newCheck.notifyOnIssues,
            notifyOnSuccess: newCheck.notifyOnSuccess,
          }
        }
        return check
      }),
    )

    setIsEditingCheck(null)

    toast({
      title: "Scheduled Check Updated",
      description: `${newCheck.name} has been updated`,
    })
  }

  const handleDeleteCheck = (checkId: string) => {
    setScheduledChecks((prev) => prev.filter((check) => check.id !== checkId))

    toast({
      title: "Scheduled Check Deleted",
      description: "The scheduled check has been removed",
    })
  }

  const handleToggleCheck = (checkId: string, enabled: boolean) => {
    setScheduledChecks((prev) =>
      prev.map((check) => {
        if (check.id === checkId) {
          return { ...check, enabled }
        }
        return check
      }),
    )

    toast({
      title: enabled ? "Check Enabled" : "Check Disabled",
      description: enabled ? "The scheduled check has been enabled" : "The scheduled check has been disabled",
    })
  }

  const handleEditCheck = (check: ScheduledCheck) => {
    setNewCheck({
      name: check.name,
      frequency: check.frequency,
      enabled: check.enabled,
      days: check.days,
      time: check.time,
      notifyOnIssues: check.notifyOnIssues,
      notifyOnSuccess: check.notifyOnSuccess,
      nextRun: check.nextRun,
    })
    setIsEditingCheck(check.id)
  }

  const getStatusIcon = (status: "operational" | "degraded" | "outage") => {
    switch (status) {
      case "operational":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case "degraded":
        return <AlertTriangle className="h-4 w-4 text-amber-500" />
      case "outage":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getDayName = (day: number) => {
    const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
    return days[day]
  }

  const getFrequencyText = (check: ScheduledCheck) => {
    switch (check.frequency) {
      case "hourly":
        return "Every hour"
      case "daily":
        return `Daily at ${check.time || "00:00"}`
      case "weekly":
        if (check.days && check.days.length > 0) {
          return `Weekly on ${check.days.map(getDayName).join(", ")} at ${check.time || "00:00"}`
        }
        return "Weekly"
      case "monthly":
        return `Monthly at ${check.time || "00:00"}`
      case "custom":
        return "Custom schedule"
      default:
        return check.frequency
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Scheduled System Checks</CardTitle>
            <CardDescription>Configure automatic system checks to run on a regular schedule</CardDescription>
          </div>
          <Button
            onClick={() => setIsAddingCheck(true)}
            disabled={isAddingCheck}
            className="bg-sage-700 hover:bg-sage-800 text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Scheduled Check
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming Checks</TabsTrigger>
            <TabsTrigger value="history">Check History</TabsTrigger>
            <TabsTrigger value="settings">Schedule Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="space-y-4">
            {scheduledChecks.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <CalendarIcon2 className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium">No Scheduled Checks</h3>
                <p className="text-muted-foreground mt-2 max-w-md">
                  You haven't set up any scheduled system checks yet. Click "Add Scheduled Check" to create one.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {scheduledChecks
                  .filter((check) => check.enabled)
                  .sort((a, b) => a.nextRun.getTime() - b.nextRun.getTime())
                  .map((check) => (
                    <Card key={check.id} className="overflow-hidden">
                      <div className="flex border-b">
                        <div className="w-2 bg-sage-600"></div>
                        <div className="flex-1 p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{check.name}</h3>
                              <Badge variant="outline" className="ml-2">
                                {check.frequency}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2">
                              <Switch
                                checked={check.enabled}
                                onCheckedChange={(checked) => handleToggleCheck(check.id, checked)}
                                aria-label="Toggle scheduled check"
                              />
                              <Button variant="ghost" size="icon" onClick={() => handleEditCheck(check)}>
                                <CalendarIcon className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button variant="ghost" size="icon" onClick={() => handleDeleteCheck(check.id)}>
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-4 mt-2">
                            <div>
                              <p className="text-sm text-muted-foreground">Next Check</p>
                              <p className="text-sm font-medium">
                                {format(check.nextRun, "PPp")} (
                                {formatDistanceToNow(check.nextRun, { addSuffix: true })})
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Frequency</p>
                              <p className="text-sm font-medium">{getFrequencyText(check)}</p>
                            </div>
                            {check.lastRun && (
                              <>
                                <div>
                                  <p className="text-sm text-muted-foreground">Last Check</p>
                                  <p className="text-sm font-medium">{format(check.lastRun, "PPp")}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-muted-foreground">Last Status</p>
                                  <div className="flex items-center gap-1">
                                    {getStatusIcon(check.lastStatus || "operational")}
                                    <p className="text-sm font-medium capitalize">{check.lastStatus || "Unknown"}</p>
                                  </div>
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}

                {scheduledChecks.filter((check) => !check.enabled).length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">Disabled Checks</h3>
                    <div className="space-y-2">
                      {scheduledChecks
                        .filter((check) => !check.enabled)
                        .map((check) => (
                          <div key={check.id} className="flex items-center justify-between p-2 border rounded-md">
                            <div className="flex items-center gap-2">
                              <BellOff className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">{check.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {check.frequency}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 text-xs"
                                onClick={() => handleToggleCheck(check.id, true)}
                              >
                                Enable
                              </Button>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            {checkHistory.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium">No Check History</h3>
                <p className="text-muted-foreground mt-2 max-w-md">
                  No system checks have been run yet. History will appear here after scheduled checks run.
                </p>
              </div>
            ) : (
              <ScrollArea className="h-[500px] pr-4">
                <div className="space-y-4">
                  {checkHistory.map((history) => (
                    <Card key={history.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(history.status)}
                            <h3 className="font-medium">{history.name}</h3>
                          </div>
                          <Badge
                            className={cn(
                              "capitalize",
                              history.status === "operational"
                                ? "bg-green-100 text-green-800"
                                : history.status === "degraded"
                                  ? "bg-amber-100 text-amber-800"
                                  : "bg-red-100 text-red-800",
                            )}
                          >
                            {history.status}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mt-2">
                          <div>
                            <p className="text-sm text-muted-foreground">Run At</p>
                            <p className="text-sm font-medium">{format(history.runAt, "PPp")}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Issues Found</p>
                            <p className="text-sm font-medium">
                              {history.issuesFound} {history.issuesFound === 1 ? "issue" : "issues"}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            {isAddingCheck || isEditingCheck ? (
              <Card>
                <CardHeader>
                  <CardTitle>{isEditingCheck ? "Edit Scheduled Check" : "Add Scheduled Check"}</CardTitle>
                  <CardDescription>Configure when and how often the system check should run</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="check-name">Check Name</Label>
                      <Input
                        id="check-name"
                        value={newCheck.name}
                        onChange={(e) => setNewCheck({ ...newCheck, name: e.target.value })}
                        placeholder="Daily System Check"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="check-frequency">Frequency</Label>
                      <Select
                        value={newCheck.frequency}
                        onValueChange={(value) =>
                          setNewCheck({
                            ...newCheck,
                            frequency: value as ScheduledCheck["frequency"],
                            // Reset days if changing from weekly
                            days: value !== "weekly" ? undefined : newCheck.days,
                          })
                        }
                      >
                        <SelectTrigger id="check-frequency">
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hourly">Hourly</SelectItem>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {(newCheck.frequency === "daily" ||
                      newCheck.frequency === "weekly" ||
                      newCheck.frequency === "monthly") && (
                      <div className="space-y-2">
                        <Label htmlFor="check-time">Time</Label>
                        <Input
                          id="check-time"
                          type="time"
                          value={newCheck.time || "09:00"}
                          onChange={(e) => setNewCheck({ ...newCheck, time: e.target.value })}
                        />
                      </div>
                    )}

                    {newCheck.frequency === "weekly" && (
                      <div className="space-y-2">
                        <Label>Days of Week</Label>
                        <div className="flex flex-wrap gap-2">
                          {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                            <Button
                              key={day}
                              type="button"
                              variant={newCheck.days?.includes(day) ? "default" : "outline"}
                              className={newCheck.days?.includes(day) ? "bg-sage-700 hover:bg-sage-800 text-white" : ""}
                              onClick={() => {
                                const days = newCheck.days || []
                                if (days.includes(day)) {
                                  setNewCheck({
                                    ...newCheck,
                                    days: days.filter((d) => d !== day),
                                  })
                                } else {
                                  setNewCheck({
                                    ...newCheck,
                                    days: [...days, day].sort(),
                                  })
                                }
                              }}
                            >
                              {getDayName(day)}
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}

                    {newCheck.frequency === "custom" && (
                      <div className="space-y-2">
                        <Label>Custom Date & Time</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn("justify-start text-left font-normal", !date && "text-muted-foreground")}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {date ? format(date, "PPP") : "Pick a date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                            </PopoverContent>
                          </Popover>
                          <Input
                            type="time"
                            value={newCheck.time || "09:00"}
                            onChange={(e) => setNewCheck({ ...newCheck, time: e.target.value })}
                          />
                        </div>
                      </div>
                    )}

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="check-enabled">Enable Check</Label>
                        <Switch
                          id="check-enabled"
                          checked={newCheck.enabled}
                          onCheckedChange={(checked) => setNewCheck({ ...newCheck, enabled: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="notify-issues">Notify on Issues</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive notifications when issues are detected
                          </p>
                        </div>
                        <Switch
                          id="notify-issues"
                          checked={newCheck.notifyOnIssues}
                          onCheckedChange={(checked) => setNewCheck({ ...newCheck, notifyOnIssues: checked })}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="notify-success">Notify on Success</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive notifications when all systems are operational
                          </p>
                        </div>
                        <Switch
                          id="notify-success"
                          checked={newCheck.notifyOnSuccess}
                          onCheckedChange={(checked) => setNewCheck({ ...newCheck, notifyOnSuccess: checked })}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAddingCheck(false)
                      setIsEditingCheck(null)
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      if (isEditingCheck) {
                        handleUpdateCheck(isEditingCheck)
                      } else {
                        handleAddCheck()
                      }
                    }}
                    className="bg-sage-700 hover:bg-sage-800 text-white"
                  >
                    <Save className="mr-2 h-4 w-4" />
                    {isEditingCheck ? "Update Check" : "Add Check"}
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Active Schedules</CardTitle>
                      <CardDescription>Currently active scheduled system checks</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {scheduledChecks.filter((check) => check.enabled).length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-8 text-center">
                          <p className="text-muted-foreground">No active scheduled checks</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {scheduledChecks
                            .filter((check) => check.enabled)
                            .map((check) => (
                              <div
                                key={check.id}
                                className="flex items-center justify-between py-2 border-b last:border-0"
                              >
                                <div>
                                  <p className="font-medium">{check.name}</p>
                                  <p className="text-sm text-muted-foreground">{getFrequencyText(check)}</p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Button variant="ghost" size="sm" onClick={() => handleEditCheck(check)}>
                                    Edit
                                  </Button>
                                  <Button variant="ghost" size="sm" onClick={() => handleToggleCheck(check.id, false)}>
                                    Disable
                                  </Button>
                                </div>
                              </div>
                            ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Notification Settings</CardTitle>
                      <CardDescription>Configure how you want to be notified about system checks</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">In-App Notifications</p>
                            <p className="text-sm text-muted-foreground">Show notifications in the Command Center</p>
                          </div>
                          <Switch defaultChecked id="in-app-notifications" />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Email Notifications</p>
                            <p className="text-sm text-muted-foreground">Send email alerts for system check results</p>
                          </div>
                          <Switch id="email-notifications" />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">Critical Issues Only</p>
                            <p className="text-sm text-muted-foreground">Only notify for critical system issues</p>
                          </div>
                          <Switch id="critical-only" />
                        </div>

                        <div className="pt-4">
                          <Label htmlFor="email-address">Email Address</Label>
                          <Input id="email-address" placeholder="<EMAIL>" className="mt-1" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle>Schedule Templates</CardTitle>
                    <CardDescription>Quick templates to create common scheduled checks</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card className="border-dashed cursor-pointer hover:bg-muted/50 transition-colors">
                        <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                          <div className="h-12 w-12 rounded-full bg-sage-100 flex items-center justify-center mb-2">
                            <Clock className="h-6 w-6 text-sage-700" />
                          </div>
                          <h3 className="font-medium">Daily Morning Check</h3>
                          <p className="text-sm text-muted-foreground mt-1">Runs every weekday at 9:00 AM</p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-4"
                            onClick={() => {
                              setNewCheck({
                                name: "Daily Morning Check",
                                frequency: "daily",
                                enabled: true,
                                days: [1, 2, 3, 4, 5],
                                time: "09:00",
                                notifyOnIssues: true,
                                notifyOnSuccess: false,
                              })
                              setIsAddingCheck(true)
                            }}
                          >
                            Use Template
                          </Button>
                        </CardContent>
                      </Card>

                      <Card className="border-dashed cursor-pointer hover:bg-muted/50 transition-colors">
                        <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                          <div className="h-12 w-12 rounded-full bg-sage-100 flex items-center justify-center mb-2">
                            <RefreshCw className="h-6 w-6 text-sage-700" />
                          </div>
                          <h3 className="font-medium">Hourly Business Hours</h3>
                          <p className="text-sm text-muted-foreground mt-1">Runs every hour during business hours</p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-4"
                            onClick={() => {
                              setNewCheck({
                                name: "Hourly Business Hours Check",
                                frequency: "hourly",
                                enabled: true,
                                days: [1, 2, 3, 4, 5],
                                notifyOnIssues: true,
                                notifyOnSuccess: false,
                              })
                              setIsAddingCheck(true)
                            }}
                          >
                            Use Template
                          </Button>
                        </CardContent>
                      </Card>

                      <Card className="border-dashed cursor-pointer hover:bg-muted/50 transition-colors">
                        <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                          <div className="h-12 w-12 rounded-full bg-sage-100 flex items-center justify-center mb-2">
                            <CalendarIcon2 className="h-6 w-6 text-sage-700" />
                          </div>
                          <h3 className="font-medium">Weekly Comprehensive</h3>
                          <p className="text-sm text-muted-foreground mt-1">Runs every Monday at 7:00 AM</p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-4"
                            onClick={() => {
                              setNewCheck({
                                name: "Weekly Comprehensive Check",
                                frequency: "weekly",
                                enabled: true,
                                days: [1], // Monday
                                time: "07:00",
                                notifyOnIssues: true,
                                notifyOnSuccess: true,
                              })
                              setIsAddingCheck(true)
                            }}
                          >
                            Use Template
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

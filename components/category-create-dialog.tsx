'use client'

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { categoryService, Category } from "@/lib/api/categories"

interface CategoryCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: any) => void
}

const PREDEFINED_COLORS = [
  "#ef4444", // red
  "#f97316", // orange
  "#eab308", // yellow
  "#22c55e", // green
  "#06b6d4", // cyan
  "#3b82f6", // blue
  "#8b5cf6", // violet
  "#ec4899", // pink
  "#64748b", // slate
  "#78716c", // stone
]

const CATEGORY_ICONS = [
  "folder",
  "tag",
  "star",
  "heart",
  "bookmark",
  "flag",
  "target",
  "trophy",
  "gift",
  "diamond",
]

export function CategoryCreateDialog({ open, onOpenChange, onSubmit }: CategoryCreateDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color: "#3b82f6",
    icon: "",
    parent_id: "",
    is_active: true,
    sort_order: 0
  })
  const [parentCategories, setParentCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (open) {
      loadParentCategories()
    }
  }, [open])

  const loadParentCategories = async () => {
    try {
      const response = await categoryService.getAllCategories()
      setParentCategories(response.data.filter(cat => cat.is_active))
    } catch (error) {
      console.error('Failed to load parent categories:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const submitData = {
        ...formData,
        parent_id: formData.parent_id || null,
        icon: formData.icon || null,
      }
      await onSubmit(submitData)
      
      // Reset form
      setFormData({
        name: "",
        description: "",
        color: "#3b82f6",
        icon: "",
        parent_id: "",
        is_active: true,
        sort_order: 0
      })
    } catch (error) {
      console.error('Failed to create category:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-autow-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-auto">
        <DialogHeader>
          <DialogTitle>Create New Category</DialogTitle>
          <DialogDescription>
            Add a new category to organize your products and content.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid md:grid-cols-2 grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Category Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Travel Gear"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                  className="w-16 h-10 p-1 border rounded"
                />
                <div className="flex flex-wrap gap-1">
                  {PREDEFINED_COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className="w-6 h-6 rounded border-2 border-gray-300 hover:border-gray-500"
                      style={{ backgroundColor: color }}
                      onClick={() => setFormData(prev => ({ ...prev, color }))}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what this category is for..."
              rows={3}
            />
          </div>

          <div className="grid md:grid-cols-2 grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="parent">Parent Category (Optional)</Label>
              <Select
                value={formData.parent_id || "none"}
                onValueChange={(value) => setFormData(prev => ({ ...prev, parent_id: value === "none" ? null : value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select parent category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Parent</SelectItem>
                  {parentCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="icon">Icon (Optional)</Label>
              <Select
                value={formData.icon || "none"}
                onValueChange={(value) => setFormData(prev => ({ ...prev, icon: value === "none" ? undefined : value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select icon" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No Icon</SelectItem>
                  {CATEGORY_ICONS.map((icon) => (
                    <SelectItem key={icon} value={icon}>
                      {icon.charAt(0).toUpperCase() + icon.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid md:grid-cols-2 grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sort_order">Sort Order</Label>
              <Input
                id="sort_order"
                type="number"
                value={formData.sort_order}
                onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                placeholder="0"
                min="0"
              />
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 pt-6">
              <div className="relative group">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                <div className="relative flex items-center space-x-3 bg-gradient-to-r from-background via-muted/10 to-background p-4 rounded-xl border border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-lg hover:shadow-primary/10">
                  <div className="relative">
                    <Checkbox
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: !!checked }))}
                      className="w-5 h-5 data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-500 data-[state=checked]:to-purple-500 data-[state=checked]:border-0 transition-all duration-300 hover:scale-110"
                    />
                    {formData.is_active && (
                      <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded blur-sm animate-pulse"></div>
                    )}
                  </div>
                  <div className="flex flex-col space-y-1">
                    <Label
                      htmlFor="is_active"
                      className="text-sm font-medium cursor-pointer hover:text-primary transition-colors duration-200"
                    >
                      Category Status
                    </Label>
                    <span className="text-xs text-muted-foreground">
                      {formData.is_active ? (
                        <span className="flex items-center space-x-1">
                          <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                          <span>Active - Visible to users</span>
                        </span>
                      ) : (
                        <span className="flex items-center space-x-1">
                          <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                          <span>Inactive - Hidden from users</span>
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {formData.name && (
            <div className="space-y-2">
              <Label>Preview Slug</Label>
              <div className="text-sm text-muted-foreground bg-muted p-2 rounded">
                {generateSlug(formData.name)}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button className="mb-2 sm:mb-0" type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button className="mb-2 sm:mb-0" type="submit" disabled={loading || !formData.name.trim()}>
              {loading ? "Creating..." : "Create Category"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

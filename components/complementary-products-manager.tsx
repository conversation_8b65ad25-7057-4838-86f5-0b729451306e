"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "@/components/ui/use-toast"
import {
  Package,
  Plus,
  Trash2,
  QrCode,
  LinkIcon,
  ArrowRight,
  ShoppingCart,
  Edit,
  Layers,
  BarChart3,
  RefreshCw,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export function ComplementaryProductsManager() {
  const [activeTab, setActiveTab] = useState("products")
  const [editingProduct, setEditingProduct] = useState<string | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [qrPreviewOpen, setQrPreviewOpen] = useState(false)
  const [linkPreviewOpen, setLinkPreviewOpen] = useState(false)

  const handleEditProduct = (product: any) => {
    setSelectedProduct(product)
    setEditDialogOpen(true)
  }

  const handleSaveProduct = () => {
    setEditDialogOpen(false)
    toast({
      title: "Product Bundle Updated",
      description: "The complementary product bundle has been updated successfully.",
    })
  }

  const handleQrPreview = (product: any) => {
    setSelectedProduct(product)
    setQrPreviewOpen(true)
  }

  const handleLinkPreview = (product: any) => {
    setSelectedProduct(product)
    setLinkPreviewOpen(true)
  }

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>Complementary Products Manager</CardTitle>
        <CardDescription>
          Create and manage product bundles to increase average order value and conversion rates
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="bg-sage-100">
            <TabsTrigger value="products" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
              Product Bundles
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900"
            >
              Bundle Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="products" className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Product Bundles</h3>
                <p className="text-sm text-muted-foreground">
                  Each product can have up to 3 complementary products included in QR codes and links
                </p>
              </div>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create New Bundle
              </Button>
            </div>

            <ScrollArea className="h-[500px]">
              <Table>
                <TableHeader className="bg-sage-50">
                  <TableRow>
                    <TableHead className="w-[250px]">Main Product</TableHead>
                    <TableHead>Avatar</TableHead>
                    <TableHead>Complementary Products</TableHead>
                    <TableHead className="text-right">Bundle Conversion</TableHead>
                    <TableHead className="text-right">Revenue Lift</TableHead>
                    <TableHead>QR Code</TableHead>
                    <TableHead>Link</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {productBundles.map((bundle, index) => (
                    <TableRow key={index} className="hover:bg-sage-50">
                      <TableCell className="font-medium">{bundle.mainProduct}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src="/placeholder-user.jpg" alt={bundle.avatar} />
                            <AvatarFallback>{bundle.avatarInitials}</AvatarFallback>
                          </Avatar>
                          <span>{bundle.avatar}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {bundle.complementaryProducts.map((product, idx) => (
                            <Badge
                              key={idx}
                              variant="outline"
                              className="bg-sage-50 text-sage-700 mr-1 whitespace-nowrap"
                            >
                              {product}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell className="text-right font-medium">{bundle.bundleConversion}%</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-1 text-green-600">
                          <span>+{bundle.revenueLift}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs"
                          onClick={() => handleQrPreview(bundle)}
                        >
                          <QrCode className="mr-1 h-3.5 w-3.5" />
                          Preview
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs"
                          onClick={() => handleLinkPreview(bundle)}
                        >
                          <LinkIcon className="mr-1 h-3.5 w-3.5" />
                          Preview
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 text-xs"
                          onClick={() => handleEditProduct(bundle)}
                        >
                          <Edit className="h-3.5 w-3.5" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Bundle Performance Analytics</h3>
                <p className="text-sm text-muted-foreground">
                  Track how complementary product bundles affect conversion rates and revenue
                </p>
              </div>
              <Button variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Data
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-sage-50">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Average Bundle Conversion</CardTitle>
                  <Layers className="h-4 w-4 text-sage-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-sage-900">18.5%</div>
                  <div className="flex items-center text-xs text-sage-600">
                    <ArrowRight className="mr-1 h-3 w-3" />
                    <span>+5.2% from single products</span>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-sage-50">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Revenue Increase</CardTitle>
                  <BarChart3 className="h-4 w-4 text-sage-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-sage-900">+24.7%</div>
                  <div className="flex items-center text-xs text-sage-600">
                    <ArrowRight className="mr-1 h-3 w-3" />
                    <span>Average revenue lift per bundle</span>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-sage-50">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Top Bundle</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-sage-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-sage-900">Fitness Set</div>
                  <div className="flex items-center text-xs text-sage-600">
                    <span>32.8% conversion rate</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Bundle Performance by Avatar</CardTitle>
                <CardDescription>Compare bundle effectiveness across different avatars</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader className="bg-sage-50">
                    <TableRow>
                      <TableHead>Avatar</TableHead>
                      <TableHead className="text-right">Avg. Bundle Conversion</TableHead>
                      <TableHead className="text-right">Revenue Lift</TableHead>
                      <TableHead className="text-right">Top Bundle</TableHead>
                      <TableHead className="text-right">QR vs Link</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {bundleAnalytics.map((item, index) => (
                      <TableRow key={index} className="hover:bg-sage-50">
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src="/placeholder-user.jpg" alt={item.avatar} />
                              <AvatarFallback>{item.avatarInitials}</AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{item.avatar}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">{item.avgBundleConversion}%</TableCell>
                        <TableCell className="text-right">+{item.revenueLift}%</TableCell>
                        <TableCell className="text-right">{item.topBundle}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <span>{item.qrPerformance}%</span>
                            <QrCode className="h-3.5 w-3.5 text-sage-600" />
                            <span>vs</span>
                            <span>{item.linkPerformance}%</span>
                            <LinkIcon className="h-3.5 w-3.5 text-sage-600" />
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <div className="space-y-1">
              <h3 className="text-lg font-medium">Bundle Settings</h3>
              <p className="text-sm text-muted-foreground">
                Configure how complementary products are displayed and tracked
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>QR Code Settings</CardTitle>
                  <CardDescription>Configure how bundles appear in QR codes</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Auto-include complementary products</Label>
                      <p className="text-xs text-muted-foreground">
                        Automatically add complementary products to QR code landing pages
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Show bundle discount</Label>
                      <p className="text-xs text-muted-foreground">Display bundle discount on QR code landing pages</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>One-click add to cart</Label>
                      <p className="text-xs text-muted-foreground">
                        Allow customers to add entire bundle to cart with one click
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="space-y-2">
                    <Label>Bundle presentation style</Label>
                    <Select defaultValue="carousel">
                      <SelectTrigger>
                        <SelectValue placeholder="Select presentation style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="carousel">Carousel</SelectItem>
                        <SelectItem value="grid">Grid</SelectItem>
                        <SelectItem value="list">List</SelectItem>
                        <SelectItem value="featured">Featured + Sidebar</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Link Settings</CardTitle>
                  <CardDescription>Configure how bundles appear in tracking links</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Auto-include complementary products</Label>
                      <p className="text-xs text-muted-foreground">
                        Automatically add complementary products to link landing pages
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Use UTM parameters</Label>
                      <p className="text-xs text-muted-foreground">
                        Add UTM parameters to track bundle performance in analytics
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Personalized recommendations</Label>
                      <p className="text-xs text-muted-foreground">
                        Show additional personalized product recommendations
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="space-y-2">
                    <Label>Default landing page</Label>
                    <Select defaultValue="product">
                      <SelectTrigger>
                        <SelectValue placeholder="Select landing page" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="product">Product Page</SelectItem>
                        <SelectItem value="bundle">Bundle Page</SelectItem>
                        <SelectItem value="cart">Pre-filled Cart</SelectItem>
                        <SelectItem value="checkout">Express Checkout</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Edit Product Bundle Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Product Bundle</DialogTitle>
            <DialogDescription>
              Update the complementary products for {selectedProduct?.mainProduct || "this product"}
            </DialogDescription>
          </DialogHeader>

          {selectedProduct && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Main Product</Label>
                <div className="flex items-center p-3 border rounded-md bg-sage-50">
                  <div className="w-10 h-10 bg-white rounded flex items-center justify-center mr-3">
                    <Package className="h-5 w-5 text-sage-600" />
                  </div>
                  <div>
                    <p className="font-medium">{selectedProduct.mainProduct}</p>
                    <p className="text-xs text-muted-foreground">{selectedProduct.avatar}</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Complementary Products</Label>
                <div className="space-y-2">
                  {selectedProduct.complementaryProducts.map((product: string, idx: number) => (
                    <div key={idx} className="flex items-center gap-2">
                      <Input defaultValue={product} className="flex-1" />
                      <Button variant="ghost" size="icon" className="h-9 w-9">
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                  {selectedProduct.complementaryProducts.length < 3 && (
                    <Button variant="outline" className="w-full">
                      <Plus className="mr-2 h-4 w-4" />
                      Add Product
                    </Button>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Bundle Discount</Label>
                <div className="flex items-center gap-2">
                  <Input type="number" defaultValue="10" className="w-20" />
                  <span>%</span>
                  <p className="text-sm text-muted-foreground">
                    Discount applied when all items are purchased together
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Bundle Priority</Label>
                <Select defaultValue="high">
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High - Show prominently</SelectItem>
                    <SelectItem value="medium">Medium - Standard display</SelectItem>
                    <SelectItem value="low">Low - Show if space available</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Priority determines how prominently this bundle is displayed in QR codes and links
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveProduct}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* QR Code Preview Dialog */}
      <Dialog open={qrPreviewOpen} onOpenChange={setQrPreviewOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>QR Code Bundle Preview</DialogTitle>
            <DialogDescription>Preview how the product bundle will appear in QR code landing pages</DialogDescription>
          </DialogHeader>

          {selectedProduct && (
            <div className="space-y-4">
              <div className="border rounded-md p-4 flex items-center justify-center bg-white">
                <div className="w-48 h-48 bg-sage-100 flex items-center justify-center">
                  <QrCode className="h-32 w-32 text-sage-800" />
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">QR Code Landing Page Preview</h4>
                <div className="border rounded-md p-4 space-y-4">
                  <div className="space-y-1">
                    <h3 className="text-lg font-medium">{selectedProduct.mainProduct}</h3>
                    <p className="text-sm text-muted-foreground">Main product from {selectedProduct.avatar}</p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Complete Your Purchase</h4>
                    <div className="grid grid-cols-3 gap-2">
                      {selectedProduct.complementaryProducts.map((product: string, idx: number) => (
                        <div key={idx} className="border rounded-md p-2 text-center">
                          <div className="w-full aspect-square bg-sage-100 rounded-md flex items-center justify-center mb-2">
                            <Package className="h-8 w-8 text-sage-600" />
                          </div>
                          <p className="text-xs font-medium truncate">{product}</p>
                          <p className="text-xs text-green-600">+$49.99</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-2 bg-sage-50 rounded-md">
                    <div>
                      <p className="text-sm font-medium">Bundle Price</p>
                      <p className="text-xs text-muted-foreground">Save 10% when purchased together</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">$179.99</p>
                      <p className="text-xs text-green-600">Save $20.00</p>
                    </div>
                  </div>

                  <Button className="w-full">
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Add Bundle to Cart
                  </Button>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Link Preview Dialog */}
      <Dialog open={linkPreviewOpen} onOpenChange={setLinkPreviewOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Link Bundle Preview</DialogTitle>
            <DialogDescription>Preview how the product bundle will appear in tracking links</DialogDescription>
          </DialogHeader>

          {selectedProduct && (
            <div className="space-y-4">
              <div className="border rounded-md p-3 bg-sage-50 font-mono text-xs overflow-x-auto">
                https://yourdomain.com/products/{selectedProduct.mainProduct.toLowerCase().replace(/\s+/g, "-")}
                ?bundle=true&utm_source=video&utm_medium=qr&utm_campaign=bundle
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Link Landing Page Preview</h4>
                <div className="border rounded-md p-4 space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-24 h-24 bg-sage-100 rounded-md flex items-center justify-center flex-shrink-0">
                      <Package className="h-12 w-12 text-sage-600" />
                    </div>
                    <div className="space-y-1">
                      <h3 className="text-lg font-medium">{selectedProduct.mainProduct}</h3>
                      <p className="text-sm text-muted-foreground">Main product from {selectedProduct.avatar}</p>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          Bundle Available
                        </Badge>
                        <Badge variant="outline" className="bg-sage-50 text-sage-700">
                          Save 10%
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Frequently Bought Together</h4>
                    <div className="flex items-center gap-2">
                      <div className="w-16 h-16 bg-sage-100 rounded-md flex items-center justify-center flex-shrink-0">
                        <Package className="h-8 w-8 text-sage-600" />
                      </div>
                      <ArrowRight className="h-4 w-4 text-sage-400 flex-shrink-0" />
                      {selectedProduct.complementaryProducts.map((product: string, idx: number) => (
                        <div key={idx} className="flex items-center">
                          <div className="w-16 h-16 bg-sage-100 rounded-md flex items-center justify-center flex-shrink-0">
                            <Package className="h-8 w-8 text-sage-600" />
                          </div>
                          {idx < selectedProduct.complementaryProducts.length - 1 && (
                            <ArrowRight className="h-4 w-4 text-sage-400 mx-2 flex-shrink-0" />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-2 bg-sage-50 rounded-md">
                    <div>
                      <p className="text-sm font-medium">Bundle Price</p>
                      <p className="text-xs text-muted-foreground">Save 10% when purchased together</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">$179.99</p>
                      <p className="text-xs text-green-600">Save $20.00</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" className="flex-1">
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      Add Main Product
                    </Button>
                    <Button className="flex-1">
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      Add Bundle
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}

// Sample data for the product bundles
const productBundles = [
  {
    mainProduct: "Premium Travel Backpack",
    avatar: "Travel Avatar",
    avatarInitials: "TR",
    complementaryProducts: ["Travel Packing Cubes", "Compact Travel Adapter", "Waterproof Toiletry Bag"],
    bundleConversion: 18.5,
    revenueLift: 24.2,
  },
  {
    mainProduct: "Smart Home Lighting Kit",
    avatar: "Home Avatar",
    avatarInitials: "HM",
    complementaryProducts: ["Smart Motion Sensor", "Voice Control Hub", "Smart Power Strip"],
    bundleConversion: 22.3,
    revenueLift: 31.5,
  },
  {
    mainProduct: "Organic Superfood Blend",
    avatar: "Health Avatar",
    avatarInitials: "HE",
    complementaryProducts: ["Stainless Steel Shaker", "Digital Nutrition Scale", "Recipe eBook"],
    bundleConversion: 15.7,
    revenueLift: 19.8,
  },
  {
    mainProduct: "Adjustable Kettlebell Set",
    avatar: "Fitness Avatar",
    avatarInitials: "FI",
    complementaryProducts: ["Workout Mat", "Resistance Bands Set", "Fitness Tracker"],
    bundleConversion: 28.4,
    revenueLift: 32.8,
  },
  {
    mainProduct: "Business Productivity Course",
    avatar: "Entrepreneur Avatar",
    avatarInitials: "EN",
    complementaryProducts: ["Premium Planner System", "Goal Setting Workbook", "Time Management Masterclass"],
    bundleConversion: 24.6,
    revenueLift: 29.3,
  },
]

// Sample data for bundle analytics
const bundleAnalytics = [
  {
    avatar: "Travel Avatar",
    avatarInitials: "TR",
    avgBundleConversion: 18.5,
    revenueLift: 24.2,
    topBundle: "Backpack + Accessories",
    qrPerformance: 62,
    linkPerformance: 38,
  },
  {
    avatar: "Home Avatar",
    avatarInitials: "HM",
    avgBundleConversion: 22.3,
    revenueLift: 31.5,
    topBundle: "Smart Home Ecosystem",
    qrPerformance: 54,
    linkPerformance: 46,
  },
  {
    avatar: "Health Avatar",
    avatarInitials: "HE",
    avgBundleConversion: 15.7,
    revenueLift: 19.8,
    topBundle: "Wellness Starter Kit",
    qrPerformance: 48,
    linkPerformance: 52,
  },
  {
    avatar: "Fitness Avatar",
    avatarInitials: "FI",
    avgBundleConversion: 28.4,
    revenueLift: 32.8,
    topBundle: "Home Gym Essentials",
    qrPerformance: 67,
    linkPerformance: 33,
  },
  {
    avatar: "Entrepreneur Avatar",
    avatarInitials: "EN",
    avgBundleConversion: 24.6,
    revenueLift: 29.3,
    topBundle: "Productivity Suite",
    qrPerformance: 41,
    linkPerformance: 59,
  },
]
